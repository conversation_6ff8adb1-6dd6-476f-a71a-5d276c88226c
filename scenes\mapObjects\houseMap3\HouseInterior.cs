using Godot;
using System;
using System.Collections.Generic;

public partial class HouseInterior : Node2D
{
	private AnimationPlayer _animationPlayer;
	private Area2D _playerDetector;
	private Sprite2D _panel;
	private bool _isPlayerInRange = false;
	private List<Button> _buffButtons = new List<Button>();
	private List<Label> _buffLabels = new List<Label>();

	public override void _Ready()
	{
		GetNode<AnimationPlayer>("AnimatedBook/AnimationPlayer").Play("Book");

		_animationPlayer = GetNode<AnimationPlayer>("AnimatedBook/CanvasLayer/AnimationPlayer");
		_playerDetector = GetNode<Area2D>("AnimatedBook/PlayerDetector");
		_panel = GetNode<Sprite2D>("AnimatedBook/CanvasLayer/Control/Panel");

		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		if (_panel != null)
		{
			_panel.Visible = false;
			SetupBuffButtons();
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed += OnEKeyPressed;
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed -= OnEKeyPressed;
		}
	}

	private void SetupBuffButtons()
	{
		for (int i = 1; i <= 6; i++)
		{
			var button = GetNode<Button>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i}/SelectBuffButton");
			var label = GetNode<Label>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i}/Label");

			if (button != null && label != null)
			{
				_buffButtons.Add(button);
				_buffLabels.Add(label);

				int buffIndex = i - 1;
				button.Pressed += () => OnBuffButtonPressed(buffIndex);
			}
		}

		UpdateBuffDisplay();
	}

	private void OnPlayerEntered(Area2D area)
	{
		_isPlayerInRange = true;
		UpdateKeyRVisibility();
	}

	private void OnPlayerExited(Area2D area)
	{
		_isPlayerInRange = false;
		UpdateKeyRVisibility();
		HidePanel();
	}

	private void OnEKeyPressed()
	{
		if (_isPlayerInRange)
		{
			TogglePanel();
		}
	}

	private void UpdateKeyRVisibility()
	{
		var keyR = GetNodeOrNull<Node2D>("Player/Keys/KeyR");
		if (keyR != null)
		{
			keyR.Visible = _isPlayerInRange;
		}
	}

	private void TogglePanel()
	{
		if (_panel == null) return;

		if (_panel.Visible)
		{
			HidePanel();
		}
		else
		{
			ShowPanel();
		}
	}

	private void ShowPanel()
	{
		if (_panel == null || _animationPlayer == null) return;

		_animationPlayer.Play("Open");
		UpdateBuffDisplay();
	}

	private void HidePanel()
	{
		if (_panel == null || _animationPlayer == null) return;

		_animationPlayer.Play("Close");
	}

	private void UpdateBuffDisplay()
	{
		var statueBuffs = GameSaveData.Instance.StatueBuffs;

		for (int i = 0; i < _buffButtons.Count && i < statueBuffs.Count; i++)
		{
			var buff = statueBuffs[i];
			var button = _buffButtons[i];
			var label = _buffLabels[i];

			if (button != null && label != null)
			{
				button.Disabled = !buff.IsUnlocked;

				string translationKey = $"STATUE_BUFF_{i + 1}_NAME";
				label.Text = Tr(translationKey);

				if (buff.IsSelected)
				{
					button.Modulate = new Color(0.8f, 1.0f, 0.8f);
				}
				else if (buff.IsUnlocked)
				{
					button.Modulate = new Color(1.0f, 1.0f, 1.0f);
				}
				else
				{
					button.Modulate = new Color(0.5f, 0.5f, 0.5f);
				}
			}
		}
	}

	private void OnBuffButtonPressed(int buffIndex)
	{
		var statueBuffs = GameSaveData.Instance.StatueBuffs;

		if (buffIndex >= 0 && buffIndex < statueBuffs.Count)
		{
			var buff = statueBuffs[buffIndex];

			if (!buff.IsUnlocked) return;

			foreach (var otherBuff in statueBuffs)
			{
				otherBuff.IsSelected = false;
			}

			buff.IsSelected = true;
			UpdateBuffDisplay();
		}
	}
}
