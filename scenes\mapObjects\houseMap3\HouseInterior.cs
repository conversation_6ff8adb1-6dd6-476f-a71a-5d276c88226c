using Godot;
using System;
using System.Collections.Generic;

public partial class HouseInterior : Node2D
{
	private AnimationPlayer _animationPlayer;
	private Area2D _playerDetector;
	private Sprite2D _panel;
	private bool _isPlayerInRange = false;
	private List<Button> _buffButtons = new List<Button>();
	private Button _closeButton;

	public override void _Ready()
	{
		GetNode<AnimationPlayer>("AnimatedBook/AnimationPlayer").Play("Book");

		_animationPlayer = GetNode<AnimationPlayer>("AnimatedBook/CanvasLayer/AnimationPlayer");
		_playerDetector = GetNode<Area2D>("AnimatedBook/PlayerDetector");
		_panel = GetNode<Sprite2D>("AnimatedBook/CanvasLayer/Control/Panel");
		_panel.Visible = false;
		
		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
			_playerDetector.AreaExited += OnPlayerExited;
		}

		if (_panel != null)
		{
			SetupBuffButtons();
		}

		_closeButton = GetNodeOrNull<Button>("AnimatedBook/CanvasLayer/Control/Panel/Close/Button");
		if (_closeButton != null)
		{
			_closeButton.Pressed += OnCloseButtonPressed;
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed += OnEKeyPressed;
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.EKeyPressed -= OnEKeyPressed;
		}
	}

	private void SetupBuffButtons()
	{
		for (int i = 1; i <= 6; i++)
		{
			var button = GetNodeOrNull<Button>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i}/SelectBuffButton");

			if (button != null)
			{
				_buffButtons.Add(button);

				int buffIndex = i - 1;
				button.Pressed += () => OnBuffButtonPressed(buffIndex);
			}
		}

		UpdateBuffDisplay();
	}

	private void OnPlayerEntered(Area2D area)
	{
		_isPlayerInRange = true;
		UpdateKeyEVisibility();
	}

	private void OnPlayerExited(Area2D area)
	{
		_isPlayerInRange = false;
		UpdateKeyEVisibility();
		HidePanel();
	}

	private void OnEKeyPressed()
	{
		if (_isPlayerInRange)
		{
			TogglePanel();
		}
	}

	private void UpdateKeyEVisibility()
	{
		var keyE = GetNodeOrNull<Node2D>("Player/Keys/KeyE");
		if (keyE != null)
		{
			keyE.Visible = _isPlayerInRange;

			if (_isPlayerInRange)
			{
				var animationPlayer = GetNodeOrNull<AnimationPlayer>("Player/Keys/KeyE/AnimationPlayer");
				if (animationPlayer != null)
				{
					animationPlayer.Play("PressKey");
				}
			}
		}
	}

	private void TogglePanel()
	{
		if (_panel == null) return;

		if (_panel.Visible)
		{
			HidePanel();
		}
		else
		{
			ShowPanel();
		}
	}

	private void ShowPanel()
	{
		if (_panel == null || _animationPlayer == null) return;

		_panel.Visible = true;
		_animationPlayer.Play("Open");
		UpdateBuffDisplay();
	}

	private void HidePanel()
	{
		if (_panel == null || _animationPlayer == null) return;

		_animationPlayer.Play("Close");
	}

	private void UpdateBuffDisplay()
	{
		var statueBuffs = GameSaveData.Instance.StatueBuffs;

		for (int i = 0; i < statueBuffs.Count && i < 6; i++)
		{
			var buff = statueBuffs[i];
			var itemNode = GetNodeOrNull<Node2D>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i + 1}");

			if (itemNode != null)
			{
				var lockSprite = GetNodeOrNull<Sprite2D>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i + 1}/LockSprite");
				var buffImage = GetNodeOrNull<Sprite2D>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i + 1}/BuffImage");
				var placeholder = GetNodeOrNull<Sprite2D>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i + 1}/Placeholder");
				var label = GetNodeOrNull<Label>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i + 1}/Label");
				var button = GetNodeOrNull<Button>($"AnimatedBook/CanvasLayer/Control/Panel/Item{i + 1}/SelectBuffButton");

				if (lockSprite != null)
					lockSprite.Visible = !buff.IsUnlocked;

				if (buffImage != null)
					buffImage.Visible = buff.IsUnlocked;

				if (placeholder != null)
					placeholder.Visible = buff.IsSelected;

				if (label != null)
				{
					if (buff.IsUnlocked)
					{
						string translationKey = $"STATUE_BUFF_{i + 1}_NAME";
						label.Text = Tr(translationKey);
					}
					else
					{
						label.Text = Tr("STATUE_NOT_DISCOVERED_YET");
					}
				}

				if (button != null)
				{
					button.Disabled = !buff.IsUnlocked;
				}
			}
		}
	}

	private void OnBuffButtonPressed(int buffIndex)
	{
		var statueBuffs = GameSaveData.Instance.StatueBuffs;

		if (buffIndex >= 0 && buffIndex < statueBuffs.Count)
		{
			var buff = statueBuffs[buffIndex];

			if (!buff.IsUnlocked) return;

			foreach (var otherBuff in statueBuffs)
			{
				otherBuff.IsSelected = false;
			}

			buff.IsSelected = true;

			var animationPlayer = GetNodeOrNull<AnimationPlayer>($"AnimatedBook/CanvasLayer/Control/Panel/Item{buffIndex + 1}/AnimationPlayer");
			if (animationPlayer != null)
			{
				animationPlayer.Play("Select");
			}

			UpdateBuffDisplay();
		}
	}

	private void OnCloseButtonPressed()
	{
		HidePanel();
	}
}
