using Godot;
using System.Collections.Generic;

public enum SceneType
{
	World,
	HouseInterior
}

public partial class Portal : Node2D
{
	[Export] public SceneType SceneFrom { get; set; } = SceneType.World;
	[Export] public SceneType SceneTo { get; set; } = SceneType.HouseInterior;

	private Area2D _playerDetector;
	private CanvasLayer _canvasLayer;
	private bool _isTransitioning = false;

	private static readonly Dictionary<SceneType, string> ScenePaths = new Dictionary<SceneType, string>
	{
		{ SceneType.World, "res://scenes/world.tscn" },
		{ SceneType.HouseInterior, "res://scenes/mapObjects/houseMap3/HouseInterior.tscn" }
	};

	private static readonly Dictionary<(SceneType from, SceneType to), Vector2> PlayerPositions = new Dictionary<(SceneType, SceneType), Vector2>
	{
		{ (SceneType.World, SceneType.HouseInterior), new Vector2(72.68f, 55f) },
		{ (SceneType.HouseInterior, SceneType.World), new Vector2(424f, 423f) }
	};

	public override void _Ready()
	{
		_playerDetector = GetNode<Area2D>("PlayerDetector");
		_canvasLayer = GetNode<CanvasLayer>("CanvasLayer");

		if (_playerDetector != null)
		{
			_playerDetector.CollisionMask = 4;
			_playerDetector.AreaEntered += OnPlayerEntered;
		}

		if (_canvasLayer != null)
		{
			_canvasLayer.Visible = false;
		}
	}

	public override void _ExitTree()
	{
		if (_playerDetector != null)
		{
			_playerDetector.AreaEntered -= OnPlayerEntered;
		}
	}

	private void OnPlayerEntered(Area2D area)
	{
		if (_isTransitioning) return;

		_isTransitioning = true;

		if (_canvasLayer != null)
		{
			_canvasLayer.Visible = true;
		}

		SetPlayerPosition();
		ChangeScene();
	}

	private void SetPlayerPosition()
	{
		var key = (SceneFrom, SceneTo);
		if (PlayerPositions.TryGetValue(key, out Vector2 position))
		{
			GameSaveData.Instance.PlayerStats.Position = position;
		}
	}

	private void ChangeScene()
	{
		if (ScenePaths.TryGetValue(SceneTo, out string scenePath))
		{
			GetTree().ChangeSceneToFile(scenePath);
		}
	}
}
