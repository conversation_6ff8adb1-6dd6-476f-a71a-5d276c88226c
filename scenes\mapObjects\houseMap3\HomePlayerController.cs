using Godot;
using System;
// just a fast copy pasete of player
public partial class HomePlayerController : CharacterBody2D
{
	[Export]
	public float Speed = 100.0f;

	[Export]
	public CustomDataLayerManager CustomDataManager { get; set; }

	[Export]
	public int TileSize { get; set; } = 16;

	private AnimationPlayer _animationPlayer;
	private Sprite2D _sprite;
	private Sprite2D _toolSprite;
	private Sprite2D _directionIndicator;
	private string _currentAnimation = "idle_down";
	private ToolType _currentTool = ToolType.None;
	private bool _isUsingTool = false;
	private string _lastDirection = "down";
	private PackedScene _arrowScene;
	private float _baseSpeed;
	private float _currentSpeedModifier = 1.0f;
	private TilePlaceholder _tilePlaceholder;
	private PackedScene _placeholderScene;
	private bool _isMoving = false;
	private Area2D _playerDetector;
	private bool _movementEnabled = true;
	private double _lastSeedPlantTime = 0.0;
	private const double SEED_PLANT_COOLDOWN = 1.2; // 1.2 seconds cooldown
	private float _lastBowShotTime = 0.0f;
	private float _bowCooldown = 1.5f;
	private float _lastBerryUseTime = 0.0f;
	private float _berryCooldown = 0.5f;
	private float _lastRabbitLegUseTime = 0.0f;
	private float _rabbitLegCooldown = 0.5f;
	private bool _swordSignalEmitted = false;
	private bool _pickaxeSignalEmitted = false;
	private PlayerLight _playerLight;

	[Export]
	public float IndicatorDistance = 25.0f;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("PlayerAnimation");
		_sprite = GetNode<Sprite2D>("PlayerSprite");
		_toolSprite = GetNode<Sprite2D>("Tool");
		_directionIndicator = GetNode<Sprite2D>("DirectionIndicator");
		_playerDetector = GetNode<Area2D>("PlayerDetector");

		// PlayerDetector collision layers are now configured in TSCN file

		_baseSpeed = Speed;

		_toolSprite.Visible = false;
		_directionIndicator.Visible = false;

		_arrowScene = GD.Load<PackedScene>("res://scenes/arrow.tscn");

		LoadPlayerState();

		// Connect to movement control signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PlayerMovementEnabled += OnPlayerMovementEnabled;
			CommonSignals.Instance.UseResourceRequested += OnUseResourceRequested;
		}

		_placeholderScene = GD.Load<PackedScene>("res://scenes/TilePlaceholder.tscn");
		if (_placeholderScene != null)
		{
			_tilePlaceholder = _placeholderScene.Instantiate<TilePlaceholder>();
			_tilePlaceholder.TileSize = TileSize;

			GetParent().CallDeferred("add_child", _tilePlaceholder);

			_tilePlaceholder.GlobalPosition = GlobalPosition + new Vector2(TileSize, 0);
			_tilePlaceholder.Visible = true;

			GD.Print("TilePlaceholder created and will be added to scene");
		}
		else
		{
			GD.PrintErr("Failed to load TilePlaceholder scene");
		}
	}

	public override void _Process(double delta)
	{
		HandleToolInput();
		UpdateDirectionIndicator();
		UpdateSpeedFromTileData();

		if (_isUsingTool || !_movementEnabled)
			return;

		Vector2 velocity = Vector2.Zero;

		if (Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W))
			velocity.Y -= 1;
		if (Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S))
			velocity.Y += 1;
		if (Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A))
			velocity.X -= 1;
		if (Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D))
			velocity.X += 1;

		if (velocity.Length() > 0)
			velocity = velocity.Normalized();

		_isMoving = velocity.Length() > 0;

		float statsSpeedModifier = PlayerStatsManager.Instance?.GetSpeedModifier() ?? 1.0f;
		Velocity = velocity * Speed * statsSpeedModifier;

		UpdateAnimation(velocity);
		Vector2 oldPosition = GlobalPosition;
		MoveAndSlide();

		// Update GameData if position changed
		if (GlobalPosition != oldPosition)
		{
			UpdatePlayerPositionInGameData();
		}
	}

	public override void _Input(InputEvent @event)
	{
		if (@event is InputEventKey keyEvent && keyEvent.Pressed && !keyEvent.Echo)
		{
			if (keyEvent.Keycode == Key.E)
			{
				CommonSignals.Instance?.EmitEKeyPressed();
			}
		}
	}

	private void HandleToolInput()
	{
		// Tool input handling can be added here if needed
	}

	private void HandleToolUse()
	{
		if (!_isUsingTool)
		{
			var rm = ResourcesManager.Instance;
			if (rm != null)
			{
				var currentItem = rm.GetQuickSelectItem(GetCurrentSelectedSlot());
				if (currentItem != null && !currentItem.IsEmpty)
				{
					if (currentItem.IsTool)
					{
						if (_currentTool != ToolType.None)
						{
							UseTool();
						}
					}
					else
					{
						UseResource(currentItem.ResourceType);
					}
				}
				else if (_currentTool != ToolType.None)
				{
					UseTool();
				}
			}
		}
	}

	public void SetCurrentTool(ToolType tool)
	{
		if (_currentTool != tool)
		{
			_currentTool = tool;
			UpdateSelectedToolInGameData();
			UpdateDirectionIndicatorVisibility();
		}
	}

	private void UseTool()
	{
		if (_currentTool == ToolType.None)
			return;

		// Check if player has energy to use tools
		if (PlayerStatsManager.Instance?.CurrentEnergy <= 0)
		{
			GD.Print("Cannot use tool: No energy remaining");
			return;
		}

		if (_currentTool == ToolType.Bow)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastBowShotTime < _bowCooldown)
			{
				return;
			}
		}

		_isUsingTool = true;
		_toolSprite.Visible = true;
		_swordSignalEmitted = false;
		_pickaxeSignalEmitted = false;

		UpdateDirectionIndicatorVisibility();

		// Update player direction based on mouse position for directional tools
		if (_currentTool == ToolType.Sword || _currentTool == ToolType.Bow)
		{
			Vector2 mousePos = GetGlobalMousePosition();
			Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();
			string newDirection = GetClosestCardinalDirection(mouseDirection);
			if (newDirection != _lastDirection)
			{
				_lastDirection = newDirection;
				UpdatePlayerDirectionInGameData();
			}
		}

		string toolAnimation = "";
		switch (_currentTool)
		{
			case ToolType.Pickaxe:
				toolAnimation = "pickaxe_" + _lastDirection;
				break;
			case ToolType.Sword:
				toolAnimation = "sword_" + _lastDirection;
				break;
			case ToolType.Bow:
				toolAnimation = "bow_" + _lastDirection;
				break;
			case ToolType.Hammer:
				toolAnimation = "hammer_" + _lastDirection;
				break;
			case ToolType.Hoe:
				toolAnimation = "hoe_" + _lastDirection;
				break;
			case ToolType.WateringCan:
				toolAnimation = "wateringcan_" + _lastDirection;
				break;
		}

		if (!string.IsNullOrEmpty(toolAnimation))
		{
			_currentAnimation = toolAnimation;
			float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
			_animationPlayer.SpeedScale = animSpeedModifier;
			_animationPlayer.Play(_currentAnimation);
			_animationPlayer.AnimationFinished += OnToolAnimationFinished;

			if (_currentTool == ToolType.Sword)
			{
				float animationLength = (float)_animationPlayer.CurrentAnimationLength / animSpeedModifier;
				GetTree().CreateTimer(animationLength * 0.5f).Timeout += OnSwordMidAnimation;
			}
			else if (_currentTool == ToolType.Pickaxe)
			{
				// Pickaxe signal should be emitted after 0.65 seconds (adjusted for animation speed)
				float delayTime = 0.65f / animSpeedModifier;
				GetTree().CreateTimer(delayTime).Timeout += OnPickaxeDelayedAction;
			}
		}
		else
		{
			ExecuteToolAction();
			_isUsingTool = false;
			_toolSprite.Visible = false;
			UpdateDirectionIndicatorVisibility();
		}
	}

	private void OnToolAnimationFinished(StringName animName)
	{
		_animationPlayer.AnimationFinished -= OnToolAnimationFinished;

		if (_currentTool != ToolType.Sword && _currentTool != ToolType.Pickaxe)
		{
			ExecuteToolAction();
		}

		_isUsingTool = false;
		_toolSprite.Visible = false;

		UpdateDirectionIndicatorVisibility();

		_currentAnimation = "idle_" + _lastDirection;
		float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
		_animationPlayer.SpeedScale = animSpeedModifier;
		_animationPlayer.Play(_currentAnimation);
	}

	private void OnSwordMidAnimation()
	{
		if (_currentTool == ToolType.Sword && !_swordSignalEmitted)
		{
			_swordSignalEmitted = true;
			ExecuteToolAction();
		}
	}

	private void OnPickaxeDelayedAction()
	{
		if (_currentTool == ToolType.Pickaxe && !_pickaxeSignalEmitted)
		{
			_pickaxeSignalEmitted = true;
			ExecuteToolAction();
		}
	}

	private void ExecuteToolAction()
	{
		Vector2I targetTile = GetLookingAtTile();

		CommonSignals.Instance?.EmitToolUsed();

		switch (_currentTool)
		{
			case ToolType.Bow:
				ShootArrow();
				_lastBowShotTime = Time.GetTicksMsec() / 1000.0f;
				break;
			case ToolType.Pickaxe:
				int pickaxeLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Pickaxe, out int level) ? level : 1;
				int damage = pickaxeLevel + 2;
				CommonSignals.Instance?.EmitPickaxeUsed(targetTile, damage);
				break;
			case ToolType.Hammer:
				int hammerLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Hammer, out int hammerLvl) ? hammerLvl : 1;
				int repairAmount = 2 + hammerLevel;
				CommonSignals.Instance?.EmitHammerUsed(targetTile, repairAmount);
				break;
			case ToolType.Hoe:
				CommonSignals.Instance?.EmitHoeUsed(targetTile);
				break;
			case ToolType.WateringCan:
				CommonSignals.Instance?.EmitWateringCanUsed(targetTile);
				break;
			case ToolType.Sword:
				Vector2 attackDirection = GetFacingDirectionVector();
				CommonSignals.Instance?.EmitSwordUsed(targetTile, GlobalPosition, attackDirection);
				break;
			default:
				GD.Print($"{_currentTool} action - not implemented yet");
				break;
		}
	}

	private void ShootArrow()
	{
		if (_arrowScene == null)
			return;

		Vector2 mousePos = GetGlobalMousePosition();
		Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();
		Vector2 shootDirection = GetValidShootingDirection(mouseDirection);

		Arrow arrow = _arrowScene.Instantiate<Arrow>();

		arrow.GlobalPosition = GlobalPosition + shootDirection * 10;

		arrow.Initialize(shootDirection);

		GetTree().CurrentScene.AddChild(arrow);
	}

	private bool UseResource(ResourceType resourceType)
	{
		var rm = ResourcesManager.Instance;
		if (rm == null) return false;

		if (resourceType == ResourceType.Berry)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastBerryUseTime < _berryCooldown)
			{
				return false;
			}

			_lastBerryUseTime = currentTime;
			PlayerStatsManager.Instance?.ConsumeBerry();

			if (rm.HasResource(ResourceType.Berry, 1))
			{
				rm.RemoveResource(ResourceType.Berry, 1);
				return true;
			}
			return false;
		}
		else if (resourceType == ResourceType.RawRabbitLeg || resourceType == ResourceType.CookedRabbitLeg)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _lastRabbitLegUseTime < _rabbitLegCooldown)
			{
				return false;
			}

			_lastRabbitLegUseTime = currentTime;

			if (resourceType == ResourceType.RawRabbitLeg)
			{
				PlayerStatsManager.Instance?.ConsumeRawRabbitLeg();
				if (rm.HasResource(ResourceType.RawRabbitLeg, 1))
				{
					rm.RemoveResource(ResourceType.RawRabbitLeg, 1);
					return true;
				}
				return false;
			}
			else if (resourceType == ResourceType.CookedRabbitLeg)
			{
				PlayerStatsManager.Instance?.ConsumeCookedRabbitLeg();
				if (rm.HasResource(ResourceType.CookedRabbitLeg, 1))
				{
					rm.RemoveResource(ResourceType.CookedRabbitLeg, 1);
					return true;
				}
				return false;
			}
		}
		else if (resourceType == ResourceType.BrownMushroom)
		{
			PlayerStatsManager.Instance?.ConsumeBrownMushroom();
			if (rm.HasResource(ResourceType.BrownMushroom, 1))
			{
				rm.RemoveResource(ResourceType.BrownMushroom, 1);
				return true;
			}
			return false;
		}
		else if (resourceType == ResourceType.BlueMushroom)
		{
			PlayerStatsManager.Instance?.ConsumeBlueMushroom();
			if (rm.HasResource(ResourceType.BlueMushroom, 1))
			{
				rm.RemoveResource(ResourceType.BlueMushroom, 1);
				return true;
			}
			return false;
		}
		else if (resourceType == ResourceType.VioletMushroom)
		{
			PlayerStatsManager.Instance?.ConsumeVioletMushroom();
			if (rm.HasResource(ResourceType.VioletMushroom, 1))
			{
				rm.RemoveResource(ResourceType.VioletMushroom, 1);
				return true;
			}
			return false;
		}
		// Handle seed bags
		else if (IsSeedBag(resourceType))
		{
			// Check cooldown to prevent multiple seed planting
			double currentTime = Time.GetUnixTimeFromSystem();
			if (currentTime - _lastSeedPlantTime < SEED_PLANT_COOLDOWN)
			{
				return false; // Still in cooldown
			}

			Vector2I tilePosition = GetLookingAtTile();

			if (rm.HasResource(resourceType, 1))
			{
				// Update last plant time
				_lastSeedPlantTime = currentTime;

				// Don't remove resource yet - wait for animation to complete
				// Emit animation request with player position and target tile
				Vector2 playerPosition = GlobalPosition;
				CommonSignals.Instance?.EmitSeedAnimationRequested(playerPosition, tilePosition, resourceType);
				return true;
			}
			return false;
		}
		else
		{
			GD.Print($"Using resource: {resourceType} - not implemented yet");
			return false;
		}

		return false;
	}

	private bool IsSeedBag(ResourceType resourceType)
	{
		return resourceType >= ResourceType.CarrotSeedBag && resourceType <= ResourceType.PurpleChilliPepperSeedBag;
	}

	private int GetCurrentSelectedSlot()
	{
		var selectedToolPanel = GetNode<SelectedToolPanel>("/root/world/SelectedToolPanel");
		if (selectedToolPanel != null)
		{
			return selectedToolPanel.GetCurrentSelectedSlot();
		}
		return 0;
	}

	private void UpdateDirectionIndicatorVisibility()
	{
		_directionIndicator.Visible = (_currentTool == ToolType.Bow || _currentTool == ToolType.Sword) && !_isUsingTool;
	}

	private void UpdateDirectionIndicator()
	{
		if (!_directionIndicator.Visible)
			return;

		Vector2 mousePos = GetGlobalMousePosition();
		Vector2 mouseDirection = (mousePos - GlobalPosition).Normalized();

		Vector2 validDirection;
		if (_currentTool == ToolType.Bow)
		{
			validDirection = GetValidShootingDirection(mouseDirection);
		}
		else if (_currentTool == ToolType.Sword)
		{
			validDirection = mouseDirection;
		}
		else
		{
			validDirection = GetValidShootingDirection(mouseDirection);
		}

		Vector2 indicatorPos = GlobalPosition + validDirection * IndicatorDistance;
		_directionIndicator.GlobalPosition = indicatorPos;

		float angle = validDirection.Angle();
		_directionIndicator.Rotation = angle;
	}

	private Vector2 GetValidShootingDirection(Vector2 mouseDirection)
	{
		if (_currentTool == ToolType.Bow)
		{
			return mouseDirection;
		}

		Vector2 facingDirection = GetFacingDirectionVector();

		float facingAngle = facingDirection.Angle();
		float mouseAngle = mouseDirection.Angle();

		float angleDiff = Mathf.AngleDifference(facingAngle, mouseAngle);

		float maxAngle = Mathf.Pi / 2;

		if (Mathf.Abs(angleDiff) <= maxAngle)
		{
			return mouseDirection;
		}
		else
		{
			float clampedAngle = facingAngle + Mathf.Sign(angleDiff) * maxAngle;
			return new Vector2(Mathf.Cos(clampedAngle), Mathf.Sin(clampedAngle));
		}
	}

	private Vector2 GetFacingDirectionVector()
	{
		return _lastDirection switch
		{
			"up" => Vector2.Up,
			"down" => Vector2.Down,
			"left" => Vector2.Left,
			"right" => Vector2.Right,
			_ => Vector2.Down
		};
	}

	private string GetClosestCardinalDirection(Vector2 direction)
	{
		float angle = direction.Angle();

		float upAngle = Vector2.Up.Angle();
		float downAngle = Vector2.Down.Angle();
		float leftAngle = Vector2.Left.Angle();
		float rightAngle = Vector2.Right.Angle();

		float upDiff = Mathf.Abs(Mathf.AngleDifference(angle, upAngle));
		float downDiff = Mathf.Abs(Mathf.AngleDifference(angle, downAngle));
		float leftDiff = Mathf.Abs(Mathf.AngleDifference(angle, leftAngle));
		float rightDiff = Mathf.Abs(Mathf.AngleDifference(angle, rightAngle));

		float minDiff = Mathf.Min(Mathf.Min(upDiff, downDiff), Mathf.Min(leftDiff, rightDiff));

		if (minDiff == upDiff) return "up";
		if (minDiff == downDiff) return "down";
		if (minDiff == leftDiff) return "left";
		return "right";
	}

	private void UpdateAnimation(Vector2 velocity)
	{

		if (_isUsingTool)
			return;

		string newAnimation = _currentAnimation;

		if (velocity.Length() == 0)
		{
			if (_currentAnimation.StartsWith("walk_"))
			{
				newAnimation = "idle_" + _currentAnimation[5..];
			}
		}
		else
		{
			string newDirection;
			if (Math.Abs(velocity.X) >= Math.Abs(velocity.Y))
			{
				newDirection = velocity.X > 0 ? "right" : "left";
				newAnimation = velocity.X > 0 ? "walk_right" : "walk_left";
			}
			else
			{
				newDirection = velocity.Y > 0 ? "down" : "up";
				newAnimation = velocity.Y > 0 ? "walk_down" : "walk_up";
			}


			if (newDirection != _lastDirection)
			{
				_lastDirection = newDirection;
				UpdatePlayerDirectionInGameData();
			}
		}

		if (newAnimation != _currentAnimation)
		{
			_currentAnimation = newAnimation;
			float animSpeedModifier = PlayerStatsManager.Instance?.GetAnimationSpeedModifier() ?? 1.0f;
			_animationPlayer.SpeedScale = animSpeedModifier;
			_animationPlayer.Play(_currentAnimation);
		}
	}

	private void UpdateSpeedFromTileData()
	{
		if (CustomDataManager == null)
			return;

		Vector2I tilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		try
		{
			var tileData = CustomDataManager.GetTileData(tilePosition);

			if (Math.Abs(tileData.SpeedModifier - _currentSpeedModifier) > 0.001f)
			{
				if(Math.Abs(tileData.SpeedModifier) < 0.001) return;
				_currentSpeedModifier = tileData.SpeedModifier;
				Speed = _baseSpeed * _currentSpeedModifier;
			}
		}
		catch (Exception ex)
		{
			GD.PrintErr($"Error getting tile data at {tilePosition}: {ex.Message}");
			if (Math.Abs(_currentSpeedModifier - 1.0f) > 0.001f)
			{
				_currentSpeedModifier = 1.0f;
				Speed = _baseSpeed;
			}
		}
	}

	private Vector2I GetFacingDirectionTileOffset()
	{
		return _lastDirection switch
		{
			"up" => Vector2I.Up,
			"down" => Vector2I.Down,
			"left" => Vector2I.Left,
			"right" => Vector2I.Right,
			_ => Vector2I.Down
		};
	}

	public Vector2I GetLookingAtTile()
	{
		Vector2I playerTilePosition = new(
			Mathf.FloorToInt(GlobalPosition.X / TileSize),
			Mathf.FloorToInt(GlobalPosition.Y / TileSize)
		);

		return playerTilePosition + GetFacingDirectionTileOffset();
	}

	private void LoadPlayerState()
	{
		var gameData = GameSaveData.Instance;

		Vector2 savedPosition = gameData.PlayerStats.Position;
		if (savedPosition != Vector2.Zero)
		{
			GlobalPosition = savedPosition;
		}

		string savedDirection = gameData.PlayerStats.LastDirection;
		if (!string.IsNullOrEmpty(savedDirection))
		{
			_lastDirection = savedDirection;
		}

		ToolType savedTool = gameData.PlayerStats.SelectedTool;
		SetCurrentTool(savedTool);

		if (CustomDataManager != null)
		{
			ResourcesManager.Instance?.LoadCustomLayerData(CustomDataManager);
		}
	}

	private void UpdatePlayerPositionInGameData()
	{
		GameSaveData.Instance.PlayerStats.Position = GlobalPosition;
	}

	private void UpdatePlayerDirectionInGameData()
	{
		GameSaveData.Instance.PlayerStats.LastDirection = _lastDirection;
	}

	private void UpdateSelectedToolInGameData()
	{
		GameSaveData.Instance.PlayerStats.SelectedTool = _currentTool;
	}

	private void OnPlayerMovementEnabled(bool enabled)
	{
		_movementEnabled = enabled;
		GD.Print($"Player movement {(enabled ? "enabled" : "disabled")}");
	}

	private void OnUseResourceRequested(ResourceType resourceType)
	{
		bool success = UseResource(resourceType);
		CommonSignals.Instance?.EmitResourceUsed(resourceType, success);
	}

	/// <summary>
	/// Take damage from enemies or environmental hazards
	/// </summary>
	public void TakeDamage(int damage)
	{
		var statsManager = PlayerStatsManager.Instance;
		if (statsManager == null) return;

		// Reduce health through stats manager
		int currentHealth = statsManager.CurrentHealth;
		int newHealth = Math.Max(0, currentHealth - damage);

		// Calculate the actual damage taken
		int actualDamage = currentHealth - newHealth;

		// Set the new health (this will trigger the health changed signal)
		statsManager.AddHealth(-actualDamage);

		GD.Print($"Player took {actualDamage} damage! Health: {newHealth}/{statsManager.MaxHealth}");

		// TODO: Add visual feedback like screen flash or damage indicator
		// TODO: Add invincibility frames to prevent spam damage

		if (newHealth <= 0)
		{
			// Player death is handled by PlayerStatsManager
			GD.Print("Player health reached 0!");
		}
	}

	public override void _ExitTree()
	{
		// Disconnect from signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PlayerMovementEnabled -= OnPlayerMovementEnabled;
			CommonSignals.Instance.UseResourceRequested -= OnUseResourceRequested;
		}

		// Player state is automatically updated in GameData, no need to save explicitly
		base._ExitTree();
	}
}
