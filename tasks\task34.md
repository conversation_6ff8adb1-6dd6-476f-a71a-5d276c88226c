I added HomeInterior.tscn, HomeInterior.cs. It contains interior of player house. I also created separate player - homePlayer.tscn and HomePlayerController.cs which are duplicated from Player.tscn and PlayerController.cs with small changes. Player can't use tools here - so i removed some parts of the code. This new file would need refactor but we won't focus on this right now. In the home, player can open panel with buffs that will be unlocked once player unlocks some statues (this will be implemented later). I want you to implement following tasks: 

1. In HouseInterior we have AnimationBook->PlayerDetector. I want you to read HouseInterior.tscn and see which layer does it use to detect player. Then in homePlayer.tscn - read which layer is used by PlayerDetector. 
* You need to adjust layers in PlayerDetector in HouseInterior.tscn
* When player is in range of PlayerDetector in HouseInterior.tscn then you need to open panel with buffs. This panel is called Panel and it's located in HouseInterior.tscn. Hide it initially. To open it, you need to play "Open" animation from AnimationPlayer located in AnimationBook->CanvasLayer->AnimationPlayer. To close it, you need to play "Close" animation. Open and Close will handle visiblility of Panel.Only initially you would need to make Panel (Sprite2D) invisible.
* We need to add to our GameSaveData a new class called StatueBuffs - it needs to contain fields: Id (string), IsUnlocked and IsSelected. There are 6 possible buffs - we need to initially (first time game start) initialize them - we need 6 buffs, each with id like StatueBuff1, StatueBuff2, etc. IsUnlocked should be false for all and IsSelected should be false for all. 
* In HouseInterior, in Panel - we have Item1, Item2... Item6. Look what they contains. Each one looks the same. They have LockSprite - should be visible if given statue buff is not unlocked. They have BuffImage - should be visible if given statue buff is unlocked. They have Placeholder - should be visible if given statue buff is selected. Label - should display buff text (if unlocked) or STATUE_NOT_DISCOVERED_YET if not unlocked - all names needs to be translatable (add to translaitons). Buff translation keys should be like BUFF_STATUE_1_DESCRIPTION, BUFF_STATUE_2_DESCRIPTION, etc. There is also a button SelectBuffButton - if buff is unlocked and not selected then when pressing this, we should first hide Placeholder from all other buffs, then use AnimationPlayer in Item to play "Select" animation. This will handle showing Placeholder. When player select a buff, we should save this to game save data (make buff selected and deselect all others). Only one buff can be selected at a time.

2. 
* When player is in range of PlayerDetector in HouseInterior.tscn then you need to allow to open panel with buffs. Player would need to press E key to open it. Use CommonSignals to emit signal when player presses E key and listen to it in HouseInterior.cs. So both conditions are required to open - player in range and player clicks E key.
* Additionally, in homePlayer.tscn (read this scene to understand it!), i added Keys->KeyR node. So this KeyR node should be only visible when player is in range of PlayerDetector in HouseInterior.tscn and when this KeyR is made visible then you need to also play in AnimationPlayer (inside KeyR node) to play 